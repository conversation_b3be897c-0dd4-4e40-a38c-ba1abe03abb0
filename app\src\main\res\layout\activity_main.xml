<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp"
    android:background="#f0f0f0">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="APPSIoT- Lab 1"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/bienvenido"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="32dp" />

    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/antenna_logo"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="TeleAhorcado"
        android:textSize="28sp"
        android:textStyle="bold"
        android:layout_marginBottom="32dp" />

    <EditText
        android:id="@+id/etNombre"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/ingresar_nombre"
        android:layout_marginBottom="16dp"
        android:padding="12dp"
        android:background="@android:drawable/edit_text" />

    <Button
        android:id="@+id/btnJugar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/jugar"
        android:textSize="16sp"
        android:padding="12dp"
        android:background="#4CAF50"
        android:textColor="@android:color/white" />

</LinearLayout>