package com.example.lab1_20212529;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

public class TemaSelectionActivity extends AppCompatActivity {

    private TextView tvNombreUsuario;
    private Button btnRedes, btnCiberseguridad, btnFibraOptica;
    private String nombreUsuario;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_tema_selection);

        nombreUsuario = getIntent().getStringExtra("nombre");

        tvNombreUsuario = findViewById(R.id.tvNombreUsuario);
        btnRedes = findViewById(R.id.btnRedes);
        btnCiberseguridad = findViewById(R.id.btnCiberseguridad);
        btnFibraOptica = findViewById(R.id.btnFibraOptica);

        tvNombreUsuario.setText("<" + nombreUsuario + ">");

        btnRedes.setOnClickListener(v -> iniciarJuego("Redes"));
        btnCiberseguridad.setOnClickListener(v -> iniciarJuego("Ciberseguridad"));
        btnFibraOptica.setOnClickListener(v -> iniciarJuego("Fibra Óptica"));
    }

    private void iniciarJuego(String tema) {
        Intent intent = new Intent(TemaSelectionActivity.this, GameActivity.class);
        intent.putExtra("nombre", nombreUsuario);
        intent.putExtra("tema", tema);
        startActivity(intent);
    }
}
