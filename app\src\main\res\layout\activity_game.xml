<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#f0f0f0">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="TeleAhorcado"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvComodin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="⭐"
            android:textSize="24sp"
            android:clickable="true"
            android:background="?android:attr/selectableItemBackground"
            android:padding="8dp" />

        <TextView
            android:id="@+id/tvComodinCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1/3"
            android:textSize="16sp"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <!-- Tema -->
    <TextView
        android:id="@+id/tvTema"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Ciberseguridad"
        android:textSize="20sp"
        android:textStyle="bold"
        android:layout_gravity="center"
        android:layout_marginTop="16dp" />

    <!-- Imagen del ahorcado -->
    <FrameLayout
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_gravity="center"
        android:layout_marginTop="16dp">

        <!-- Antena base (siempre visible) -->
        <ImageView
            android:id="@+id/ivAntena"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/antenna_sinfondo" />

        <!-- Cabeza -->
        <ImageView
            android:id="@+id/ivCabeza"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/head1_sinfondo"
            android:visibility="gone" />

        <!-- Torso -->
        <ImageView
            android:id="@+id/ivTorso"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/torso_sinfondo"
            android:visibility="gone" />

        <!-- Brazo izquierdo -->
        <ImageView
            android:id="@+id/ivBrazoIzq"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/brazoizq_piernadere_sinfondo"
            android:visibility="gone" />

        <!-- Brazo derecho -->
        <ImageView
            android:id="@+id/ivBrazoDer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/brazodere_piernaiq_sinfondo"
            android:visibility="gone" />

        <!-- Pierna derecha -->
        <ImageView
            android:id="@+id/ivPiernaDer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/brazoizq_piernadere_sinfondo"
            android:visibility="gone" />

        <!-- Pierna izquierda -->
        <ImageView
            android:id="@+id/ivPiernaIzq"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/brazodere_piernaiq_sinfondo"
            android:visibility="gone" />

    </FrameLayout>

    <!-- Palabra a adivinar -->
    <TextView
        android:id="@+id/tvPalabra"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="P _ _ _ X _ Y"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_gravity="center"
        android:layout_marginTop="16dp"
        android:fontFamily="monospace" />

    <!-- Alfabeto -->
    <GridLayout
        android:id="@+id/gridAlfabeto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:columnCount="7"
        android:rowCount="4"
        android:layout_gravity="center">

    </GridLayout>

    <!-- Botón Nuevo Juego -->
    <Button
        android:id="@+id/btnNuevoJuego"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/nuevo_juego"
        android:layout_gravity="center"
        android:layout_marginTop="24dp"
        android:background="#4CAF50"
        android:textColor="@android:color/white" />

</LinearLayout>
