package com.example.lab1_20212529;

import android.graphics.Color;
import android.os.Bundle;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import java.util.List;

public class EstadisticasActivity extends AppCompatActivity {

    private TextView tvJugador, tvInicio, tvCantidadPartidas;
    private LinearLayout llPartidas;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_estadisticas);

        initViews();
        cargarEstadisticas();
    }

    private void initViews() {
        tvJugador = findViewById(R.id.tvJugador);
        tvInicio = findViewById(R.id.tvInicio);
        tvCantidadPartidas = findViewById(R.id.tvCantidadPartidas);
        llPartidas = findViewById(R.id.llPartidas);
    }

    private void cargarEstadisticas() {
        String jugador = EstadisticasManager.obtenerJugador(this);
        String inicio = EstadisticasManager.obtenerInicio(this);
        int cantidadPartidas = EstadisticasManager.obtenerCantidadPartidas(this);

        if (jugador.isEmpty()) {
            tvJugador.setText("Jugador: <nombre>");
            tvInicio.setText("Inicio: 04/09/2025 - 08:51pm");
            tvCantidadPartidas.setText("Cantidad de partidas: 0");
        } else {
            tvJugador.setText("Jugador: <" + jugador + ">");
            tvInicio.setText("Inicio: " + inicio);
            tvCantidadPartidas.setText("Cantidad de partidas: " + cantidadPartidas);
        }

        List<EstadisticasManager.Partida> partidas = EstadisticasManager.obtenerPartidas(this);
        
        for (EstadisticasManager.Partida partida : partidas) {
            TextView tvPartida = new TextView(this);
            
            String texto = "Juego " + partida.numero + ": " + partida.resultado + " / Tiempo: " + partida.tiempo;
            tvPartida.setText(texto);
            tvPartida.setTextSize(14);
            tvPartida.setPadding(16, 8, 16, 8);
            
            // Colorear según resultado
            if (partida.resultado.equals("Ganó")) {
                tvPartida.setTextColor(Color.GREEN);
            } else if (partida.resultado.equals("Perdió")) {
                tvPartida.setTextColor(Color.RED);
            } else if (partida.resultado.equals("Cancelado")) {
                tvPartida.setTextColor(Color.YELLOW);
            } else {
                tvPartida.setTextColor(Color.BLUE); // En Curso
            }
            
            llPartidas.addView(tvPartida);
        }
        
        // Si no hay partidas, mostrar ejemplos
        if (partidas.isEmpty()) {
            agregarPartidaEjemplo("Juego 7: Ganó / Tiempo: 15s", Color.GREEN);
            agregarPartidaEjemplo("Juego 8: Ganó / Tiempo: 31s", Color.GREEN);
            agregarPartidaEjemplo("Juego 9: Perdió / Tiempo: 10s", Color.RED);
            agregarPartidaEjemplo("Juego 10: Ganó / Tiempo: 16s", Color.GREEN);
            agregarPartidaEjemplo("Juego 11: Cancelado / Tiempo: 24s", Color.YELLOW);
            agregarPartidaEjemplo("Juego 12: En Curso", Color.BLUE);
        }
    }

    private void agregarPartidaEjemplo(String texto, int color) {
        TextView tvPartida = new TextView(this);
        tvPartida.setText(texto);
        tvPartida.setTextSize(14);
        tvPartida.setPadding(16, 8, 16, 8);
        tvPartida.setTextColor(color);
        llPartidas.addView(tvPartida);
    }
}
