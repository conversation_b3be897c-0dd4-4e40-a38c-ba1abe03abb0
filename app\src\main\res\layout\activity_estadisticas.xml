<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#f0f0f0">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="TeleAhorcado"
            android:textSize="18sp"
            android:textStyle="bold" />

    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/estadisticas"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tvJugador"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/jugador"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/tvInicio"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/inicio"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/tvCantidadPartidas"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/cantidad_partidas"
        android:textSize="16sp"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/partidas"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:id="@+id/llPartidas"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Las partidas se agregarán dinámicamente aquí -->

        </LinearLayout>

    </ScrollView>

</LinearLayout>
