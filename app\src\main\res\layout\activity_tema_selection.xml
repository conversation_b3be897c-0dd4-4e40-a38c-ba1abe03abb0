<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp"
    android:background="#f0f0f0">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="APPSIoT- Lab 1"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/bienvenido"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tvNombreUsuario"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="&lt;Nombre&gt;"
        android:textSize="20sp"
        android:layout_marginBottom="32dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/seleccione_tematica"
        android:textSize="18sp"
        android:layout_marginBottom="24dp" />

    <Button
        android:id="@+id/btnRedes"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:text="@string/redes"
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        android:background="#2196F3"
        android:textColor="@android:color/white" />

    <Button
        android:id="@+id/btnCiberseguridad"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:text="@string/ciberseguridad"
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        android:background="#FF9800"
        android:textColor="@android:color/white" />

    <Button
        android:id="@+id/btnFibraOptica"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:text="@string/fibra_optica"
        android:textSize="16sp"
        android:background="#9C27B0"
        android:textColor="@android:color/white" />

</LinearLayout>
