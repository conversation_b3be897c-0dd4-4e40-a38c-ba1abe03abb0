package com.example.lab1_20212529;

import android.content.Context;
import android.content.SharedPreferences;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class EstadisticasManager {
    
    private static final String PREFS_NAME = "TeleAhorcadoStats";
    private static final String KEY_PARTIDAS = "partidas";
    private static final String KEY_CONTADOR = "contador";
    private static final String KEY_JUGADOR = "jugador";
    private static final String KEY_INICIO = "inicio";
    
    public static class Partida {
        public String resultado;
        public String tiempo;
        public int numero;
        
        public Partida(String resultado, String tiempo, int numero) {
            this.resultado = resultado;
            this.tiempo = tiempo;
            this.numero = numero;
        }
    }
    
    public static void guardarPartida(Context context, String resultado, long tiempoSegundos, String jugador) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        // Guardar jugador si es la primera vez
        if (prefs.getString(KEY_JUGADOR, "").isEmpty()) {
            editor.putString(KEY_JUGADOR, jugador);
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy - HH:mm", Locale.getDefault());
            editor.putString(KEY_INICIO, sdf.format(new Date()));
        }
        
        int contador = prefs.getInt(KEY_CONTADOR, 0) + 1;
        editor.putInt(KEY_CONTADOR, contador);
        
        String tiempo = tiempoSegundos + "s";
        String partidaData = "Juego " + contador + ": " + resultado + " / Tiempo: " + tiempo;
        
        String partidasExistentes = prefs.getString(KEY_PARTIDAS, "");
        if (!partidasExistentes.isEmpty()) {
            partidasExistentes += "|";
        }
        partidasExistentes += partidaData;
        
        editor.putString(KEY_PARTIDAS, partidasExistentes);
        editor.apply();
    }
    
    public static List<Partida> obtenerPartidas(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        String partidasData = prefs.getString(KEY_PARTIDAS, "");
        
        List<Partida> partidas = new ArrayList<>();
        if (!partidasData.isEmpty()) {
            String[] partidasArray = partidasData.split("\\|");
            for (String partidaStr : partidasArray) {
                // Parsear: "Juego X: Resultado / Tiempo: Ys"
                String[] partes = partidaStr.split(": ");
                if (partes.length >= 3) {
                    int numero = Integer.parseInt(partes[0].replace("Juego ", ""));
                    String[] resultadoTiempo = partes[1].split(" / Tiempo: ");
                    String resultado = resultadoTiempo[0];
                    String tiempo = resultadoTiempo[1];
                    partidas.add(new Partida(resultado, tiempo, numero));
                }
            }
        }
        return partidas;
    }
    
    public static String obtenerJugador(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getString(KEY_JUGADOR, "");
    }
    
    public static String obtenerInicio(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getString(KEY_INICIO, "");
    }
    
    public static int obtenerCantidadPartidas(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getInt(KEY_CONTADOR, 0);
    }
}
