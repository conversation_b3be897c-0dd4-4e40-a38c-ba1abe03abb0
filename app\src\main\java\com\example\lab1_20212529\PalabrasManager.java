package com.example.lab1_20212529;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class PalabrasManager {
    
    private static final String[][] PALABRAS = {
        // Redes
        {"ROUTER", "SWITCH", "PROTOCOL<PERSON>", "ETHERNET", "WIF<PERSON>", "FIREWALL", "GATEWAY", "SUBNET"},
        // Ciberseguridad  
        {"PROXY", "MALWA<PERSON>", "PHISHING", "ANTIVIRUS", "ENCRIPTACION", "HACKER", "VULNERABILIDAD", "AUTENTICACION"},
        // Fibra Óptica
        {"FIBRA", "LASER", "OP<PERSON><PERSON>", "TRANSMISION", "BA<PERSON>WIDTH", "ATENUAC<PERSON>", "DISPER<PERSON><PERSON>", "MULTIPLEXOR"}
    };
    
    private static final String[] TEMAS = {"Redes", "Ciberseguridad", "Fibra Óptica"};
    
    public static String obtenerPalabraAleatoria(String tema) {
        int indice = obtenerIndiceTema(tema);
        if (indice == -1) return "PALABRA";
        
        String[] palabras = PALABRAS[indice];
        Random random = new Random();
        return palabras[random.nextInt(palabras.length)];
    }
    
    private static int obtenerIndiceTema(String tema) {
        for (int i = 0; i < TEMAS.length; i++) {
            if (TEMAS[i].equals(tema)) {
                return i;
            }
        }
        return -1;
    }
}
