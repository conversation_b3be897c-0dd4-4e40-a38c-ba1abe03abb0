package com.example.lab1_20212529;

import android.graphics.Color;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.View;
import android.widget.Button;
import android.widget.GridLayout;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import java.util.ArrayList;
import java.util.List;

public class GameActivity extends AppCompatActivity {

    private TextView tvTema, tvPalabra, tvComodin, tvComodinCount;
    private ImageView ivAntena, ivCabeza, ivTorso, ivBrazoIzq, ivBrazoDer, ivPiernaDer, ivPiernaIzq;
    private GridLayout gridAlfabeto;
    private Button btnNuevoJuego;
    
    private String palabraSecreta;
    private StringBuilder palabraMostrada;
    private List<Character> letrasUsadas;
    private int errores;
    private int comodinesUsados;
    private long tiempoInicio;
    private String nombreJugador;
    private String tema;
    private boolean juegoTerminado;
    
    private static final int MAX_ERRORES = 6;
    private static final int MAX_COMODINES = 3;
    private static final String ALFABETO = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_game);

        nombreJugador = getIntent().getStringExtra("nombre");
        tema = getIntent().getStringExtra("tema");

        initViews();
        iniciarNuevoJuego();
    }

    private void initViews() {
        tvTema = findViewById(R.id.tvTema);
        tvPalabra = findViewById(R.id.tvPalabra);
        tvComodin = findViewById(R.id.tvComodin);
        tvComodinCount = findViewById(R.id.tvComodinCount);

        // Inicializar las partes del ahorcado
        ivAntena = findViewById(R.id.ivAntena);
        ivCabeza = findViewById(R.id.ivCabeza);
        ivTorso = findViewById(R.id.ivTorso);
        ivBrazoIzq = findViewById(R.id.ivBrazoIzq);
        ivBrazoDer = findViewById(R.id.ivBrazoDer);
        ivPiernaDer = findViewById(R.id.ivPiernaDer);
        ivPiernaIzq = findViewById(R.id.ivPiernaIzq);

        gridAlfabeto = findViewById(R.id.gridAlfabeto);
        btnNuevoJuego = findViewById(R.id.btnNuevoJuego);

        tvComodin.setOnClickListener(v -> usarComodin());
        btnNuevoJuego.setOnClickListener(v -> iniciarNuevoJuego());
    }

    private void iniciarNuevoJuego() {
        palabraSecreta = PalabrasManager.obtenerPalabraAleatoria(tema);
        palabraMostrada = new StringBuilder();
        letrasUsadas = new ArrayList<>();
        errores = 0;
        comodinesUsados = 0;
        juegoTerminado = false;
        tiempoInicio = SystemClock.elapsedRealtime();

        tvTema.setText(tema);
        
        // Inicializar palabra mostrada con guiones
        for (int i = 0; i < palabraSecreta.length(); i++) {
            palabraMostrada.append("_ ");
        }
        
        actualizarPalabraMostrada();
        actualizarComodines();
        reiniciarImagenAhorcado();
        actualizarImagenAhorcado();
        crearAlfabeto();
    }

    private void crearAlfabeto() {
        gridAlfabeto.removeAllViews();
        
        for (char letra : ALFABETO.toCharArray()) {
            Button btnLetra = new Button(this);
            btnLetra.setText(String.valueOf(letra));
            btnLetra.setTextSize(14);
            btnLetra.setPadding(8, 8, 8, 8);
            
            GridLayout.LayoutParams params = new GridLayout.LayoutParams();
            params.width = 0;
            params.height = GridLayout.LayoutParams.WRAP_CONTENT;
            params.columnSpec = GridLayout.spec(GridLayout.UNDEFINED, 1f);
            params.setMargins(4, 4, 4, 4);
            btnLetra.setLayoutParams(params);
            
            btnLetra.setOnClickListener(v -> seleccionarLetra(letra, btnLetra));
            
            gridAlfabeto.addView(btnLetra);
        }
    }

    private void seleccionarLetra(char letra, Button boton) {
        if (juegoTerminado || letrasUsadas.contains(letra)) {
            return;
        }

        letrasUsadas.add(letra);
        boton.setEnabled(false);
        boton.setBackgroundColor(Color.GRAY);

        if (palabraSecreta.contains(String.valueOf(letra))) {
            // Letra correcta
            boton.setBackgroundColor(Color.GREEN);
            actualizarPalabraMostrada();
            
            if (!palabraMostrada.toString().contains("_")) {
                // Ganó
                juegoTerminado = true;
                long tiempoTranscurrido = (SystemClock.elapsedRealtime() - tiempoInicio) / 1000;
                EstadisticasManager.guardarPartida(this, "Ganó", tiempoTranscurrido, nombreJugador);
                Toast.makeText(this, "¡Felicidades! Ganaste en " + tiempoTranscurrido + " segundos", Toast.LENGTH_LONG).show();
            }
        } else {
            // Letra incorrecta
            boton.setBackgroundColor(Color.RED);
            errores++;
            actualizarImagenAhorcado();

            if (errores >= MAX_ERRORES) {
                // Perdió
                juegoTerminado = true;
                long tiempoTranscurrido = (SystemClock.elapsedRealtime() - tiempoInicio) / 1000;
                EstadisticasManager.guardarPartida(this, "Perdió", tiempoTranscurrido, nombreJugador);
                Toast.makeText(this, "Perdiste. La palabra era: " + palabraSecreta, Toast.LENGTH_LONG).show();

                // Mostrar palabra completa
                palabraMostrada = new StringBuilder();
                for (int i = 0; i < palabraSecreta.length(); i++) {
                    palabraMostrada.append(palabraSecreta.charAt(i)).append(" ");
                }
                tvPalabra.setText(palabraMostrada.toString().trim());
            }
        }
    }

    private void actualizarPalabraMostrada() {
        palabraMostrada = new StringBuilder();
        for (int i = 0; i < palabraSecreta.length(); i++) {
            char letra = palabraSecreta.charAt(i);
            if (letrasUsadas.contains(letra)) {
                palabraMostrada.append(letra).append(" ");
            } else {
                palabraMostrada.append("_ ");
            }
        }
        tvPalabra.setText(palabraMostrada.toString().trim());
    }

    private void usarComodin() {
        if (juegoTerminado || comodinesUsados >= MAX_COMODINES) {
            Toast.makeText(this, "No tienes comodines disponibles", Toast.LENGTH_SHORT).show();
            return;
        }

        // Encontrar una letra no revelada
        for (char letra : palabraSecreta.toCharArray()) {
            if (!letrasUsadas.contains(letra)) {
                comodinesUsados++;
                letrasUsadas.add(letra);
                actualizarPalabraMostrada();
                actualizarComodines();
                
                // Deshabilitar botón de la letra en el alfabeto
                for (int i = 0; i < gridAlfabeto.getChildCount(); i++) {
                    Button btn = (Button) gridAlfabeto.getChildAt(i);
                    if (btn.getText().toString().equals(String.valueOf(letra))) {
                        btn.setEnabled(false);
                        btn.setBackgroundColor(Color.YELLOW);
                        break;
                    }
                }
                
                Toast.makeText(this, "Comodín usado. Letra revelada: " + letra, Toast.LENGTH_SHORT).show();
                
                if (!palabraMostrada.toString().contains("_")) {
                    // Ganó
                    juegoTerminado = true;
                    long tiempoTranscurrido = (SystemClock.elapsedRealtime() - tiempoInicio) / 1000;
                    EstadisticasManager.guardarPartida(this, "Ganó", tiempoTranscurrido, nombreJugador);
                    Toast.makeText(this, "¡Felicidades! Ganaste en " + tiempoTranscurrido + " segundos", Toast.LENGTH_LONG).show();
                }
                break;
            }
        }
    }

    private void actualizarComodines() {
        int comodinesRestantes = MAX_COMODINES - comodinesUsados;
        tvComodinCount.setText(comodinesRestantes + "/" + MAX_COMODINES);
    }

    private void reiniciarImagenAhorcado() {
        // Ocultar todas las partes del ahorcado al inicio
        ivCabeza.setVisibility(View.GONE);
        ivTorso.setVisibility(View.GONE);
        ivBrazoIzq.setVisibility(View.GONE);
        ivBrazoDer.setVisibility(View.GONE);
        ivPiernaDer.setVisibility(View.GONE);
        ivPiernaIzq.setVisibility(View.GONE);
    }

    private void actualizarImagenAhorcado() {
        // Mostrar partes del ahorcado según el número de errores
        switch (errores) {
            case 1:
                ivCabeza.setVisibility(View.VISIBLE);
                break;
            case 2:
                ivTorso.setVisibility(View.VISIBLE);
                break;
            case 3:
                ivBrazoIzq.setVisibility(View.VISIBLE);
                break;
            case 4:
                ivBrazoDer.setVisibility(View.VISIBLE);
                break;
            case 5:
                ivPiernaDer.setVisibility(View.VISIBLE);
                break;
            case 6:
                ivPiernaIzq.setVisibility(View.VISIBLE);
                break;
        }
    }
}
